<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Tests</title>
    <style>
        body { font-family: monospace; padding: 20px; background: #1a1a1a; color: #fff; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .pass { background: #2d5a2d; border-left: 4px solid #4caf50; }
        .fail { background: #5a2d2d; border-left: 4px solid #f44336; }
        .test-section { margin: 20px 0; border-top: 1px solid #444; padding-top: 20px; }
        #game-container { display: none; }
        .summary { font-size: 18px; font-weight: bold; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>🧪 Contra-like Game Tests</h1>
    <div id="test-results"></div>
    
    <!-- Hidden game container for testing -->
    <div id="game-container">
        <canvas id="c" width="960" height="540"></canvas>
    </div>

    <script>
        // Test framework
        let testResults = [];
        let currentTest = '';

        function assert(condition, message) {
            const result = {
                test: currentTest,
                message: message,
                passed: condition
            };
            testResults.push(result);
            
            const div = document.createElement('div');
            div.className = `test-result ${condition ? 'pass' : 'fail'}`;
            div.innerHTML = `${condition ? '✅' : '❌'} ${currentTest}: ${message}`;
            document.getElementById('test-results').appendChild(div);
        }

        function test(name, fn) {
            currentTest = name;
            const section = document.createElement('div');
            section.className = 'test-section';
            section.innerHTML = `<h3>Testing: ${name}</h3>`;
            document.getElementById('test-results').appendChild(section);
            
            try {
                fn();
            } catch (error) {
                assert(false, `Test threw error: ${error.message}`);
            }
        }

        // Load the game code (simplified version for testing)
        const canvas = document.getElementById('c');
        const ctx = canvas.getContext('2d');
        const W = canvas.width, H = canvas.height;

        // Game state variables (copied from main game)
        let keys = {};
        let bullets = [];
        let enemies = [];
        let platforms = [];
        let score = 0; let lives = 3; let level = 1;
        let cameraX = 0;
        let worldWidth = 4000;
        let levelDistance = 2000;
        let maxPlayerX = 0;
        let lastTime = 0;
        let gameOver = false;

        const player = {
            x: 80, y: H-120, w: 28, h: 40,
            vx: 0, vy: 0, speed: 240, onGround: false, canShoot: true, shootCooldown: 0.2
        };

        // Copy key game functions for testing
        function spawnWave(n){
            for(let i=0;i<n;i++){
                const minX = Math.max(player.x + 200, cameraX + W);
                const maxX = Math.min(worldWidth - 100, minX + 800);
                const ex = minX + Math.random()*(maxX - minX);
                enemies.push({x:ex,y:H-100,w:28,h:36,vx:-60 - Math.random()*80, hp:1, shootTimer: Math.random()*2});
            }
        }

        function buildLevel(){
            platforms = [];
            let groundSegments = [{x:0, y:H-60, w:worldWidth, h:60}];
            
            const terrainSeed = level * 123;
            
            for(let x = 0; x < worldWidth; x += 200) {
                const distanceFromEnd = worldWidth - x;
                const nearLevelEnd = distanceFromEnd < 300;
                const terrainType = nearLevelEnd ? 5 : Math.floor((Math.sin(x * 0.001 + terrainSeed) + 1) * 3);
                
                switch(terrainType) {
                    case 0:
                        if(x > 300) {
                            platforms.push({x: x, y: H-160, w: 120, h: 16});
                            platforms.push({x: x + 150, y: H-220, w: 100, h: 16});
                        }
                        break;
                    case 1:
                        if(x > 300) {
                            for(let i = 0; i < 4; i++) {
                                platforms.push({x: x + i*40, y: H-100 - i*30, w: 50, h: 16});
                            }
                        }
                        break;
                    case 2:
                        if(x > 300 && x < worldWidth - 300) {
                            const valleyStart = x;
                            const valleyEnd = x + 200;
                            const newSegments = [];
                            
                            for(let segment of groundSegments) {
                                if(segment.x + segment.w <= valleyStart || segment.x >= valleyEnd) {
                                    newSegments.push(segment);
                                } else {
                                    if(segment.x < valleyStart) {
                                        newSegments.push({x: segment.x, y: segment.y, w: valleyStart - segment.x, h: segment.h});
                                    }
                                    if(segment.x + segment.w > valleyEnd) {
                                        newSegments.push({x: valleyEnd, y: segment.y, w: (segment.x + segment.w) - valleyEnd, h: segment.h});
                                    }
                                }
                            }
                            groundSegments = newSegments;
                            platforms.push({x: x + 50, y: H-140, w: 100, h: 12});
                        }
                        break;
                    case 3:
                        if(x > 300) {
                            platforms.push({x: x, y: H-120, w: 180, h: 80});
                            platforms.push({x: x + 40, y: H-180, w: 100, h: 20});
                        }
                        break;
                    case 4:
                        if(x > 300 && !nearLevelEnd) {
                            platforms.push({x: x + 20, y: H-100, w: 20, h: 40});
                            platforms.push({x: x + 80, y: H-140, w: 60, h: 16});
                            platforms.push({x: x + 160, y: H-100, w: 20, h: 40});
                        }
                        break;
                    default:
                        if(x > 300 && Math.random() > 0.6) {
                            platforms.push({x: x + Math.random()*100, y: H-120 - Math.random()*80, w: 80 + Math.random()*40, h: 16});
                        }
                        break;
                }
            }
            
            platforms.push(...groundSegments);
        }

        function init(){
            bullets=[]; enemies=[]; platforms=[]; score=0; lives=3; level=1; cameraX=0; gameOver=false;
            levelDistance = 2000; maxPlayerX = 0;
            player.x = 80; player.y = H-120; player.vx=0; player.vy=0; player.onGround=false; player.shootTimer=0;
            buildLevel();
        }

        // Run tests
        function runTests() {
            init();

            test("Enemy Spawning Logic", () => {
                player.x = 1000;
                cameraX = 800;
                enemies = [];
                spawnWave(5);
                
                let enemiesBehindPlayer = enemies.filter(e => e.x < player.x).length;
                assert(enemiesBehindPlayer === 0, `No enemies spawn behind player (found ${enemiesBehindPlayer} behind)`);
                
                let enemiesAhead = enemies.filter(e => e.x >= player.x + 200).length;
                assert(enemiesAhead === 5, `All enemies spawn ahead of player (${enemiesAhead}/5 ahead)`);
            });

            test("Distance Calculation", () => {
                player.x = 1500;
                level = 1;
                levelDistance = 2000;
                
                let currentLevelStart = 0;
                for(let i = 1; i < level; i++) {
                    if(i === 1) {
                        currentLevelStart += 2000;
                    } else {
                        currentLevelStart += 1500 + (i * 300);
                    }
                }
                
                const currentDistance = Math.max(0, player.x - currentLevelStart);
                const totalLevelDistance = levelDistance - currentLevelStart;
                const progress = ((player.x - currentLevelStart) / (levelDistance - currentLevelStart)) * 100;
                
                assert(currentDistance === 1500, `Current distance calculated correctly: ${currentDistance}m`);
                assert(totalLevelDistance === 2000, `Total level distance correct: ${totalLevelDistance}m`);
                assert(Math.abs(progress - 75) < 1, `Progress calculation correct: ${progress.toFixed(1)}%`);
            });

            test("Level End Blocking Prevention", () => {
                init();
                buildLevel();
                
                // Check last 300px of level for blocking obstacles
                const levelEndStart = worldWidth - 300;
                const blockingObstacles = platforms.filter(p => 
                    p.x >= levelEndStart && 
                    p.y < H-60 && 
                    p.h >= 40 && 
                    p.w >= 20
                ).length;
                
                assert(blockingObstacles === 0, `No blocking obstacles in last 300px (found ${blockingObstacles})`);
            });

            test("Valley Ground Removal", () => {
                init();
                buildLevel();
                
                // Check if ground segments were properly split
                const groundPlatforms = platforms.filter(p => p.h === 60 && p.y === H-60);
                const hasMultipleGroundSegments = groundPlatforms.length > 1;
                
                assert(hasMultipleGroundSegments, `Ground split into multiple segments for valleys (${groundPlatforms.length} segments)`);
                
                // Check for bridges
                const bridges = platforms.filter(p => p.h === 12 && p.y === H-140);
                assert(bridges.length > 0, `Bridges created across valleys (${bridges.length} bridges)`);
            });

            test("Terrain Variety", () => {
                init();
                buildLevel();
                
                const platformTypes = new Set();
                platforms.forEach(p => {
                    if(p.h === 60) platformTypes.add('ground');
                    else if(p.h === 16) platformTypes.add('platform');
                    else if(p.h === 12) platformTypes.add('bridge');
                    else if(p.h === 40) platformTypes.add('wall');
                    else if(p.h === 80) platformTypes.add('hill');
                    else platformTypes.add('other');
                });
                
                assert(platformTypes.size >= 3, `Multiple terrain types generated (${platformTypes.size} types: ${Array.from(platformTypes).join(', ')})`);
            });

            test("Level Progression Logic", () => {
                init();
                player.x = 2000;
                maxPlayerX = 2000;
                const initialLevel = level;
                const initialLevelDistance = levelDistance;
                
                // Simulate level completion check
                if(maxPlayerX >= levelDistance){
                    level++;
                    levelDistance += 1500 + (level * 300);
                }
                
                assert(level === initialLevel + 1, `Level incremented on completion (${initialLevel} -> ${level})`);
                assert(levelDistance > initialLevelDistance, `Level distance increased (${initialLevelDistance} -> ${levelDistance})`);
            });

            // Summary
            const passed = testResults.filter(r => r.passed).length;
            const total = testResults.length;
            const summary = document.createElement('div');
            summary.className = 'summary';
            summary.innerHTML = `<h2>Test Summary: ${passed}/${total} tests passed ${passed === total ? '🎉' : '⚠️'}</h2>`;
            document.getElementById('test-results').appendChild(summary);
        }

        // Run tests when page loads
        window.onload = runTests;
    </script>
</body>
</html>
