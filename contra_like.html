<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Contra-like — Single File</title>
  <style>
    :root{--bg:#0b1220;--ground:#274156;--player:#f2d16b;--enemy:#e05d5d;--bullet:#ffffff}
    html,body{height:100%;margin:0;background:var(--bg);font-family:system-ui,Segoe UI,Roboto}
    #game{display:block;margin:10px auto;background:#102030;border:6px solid #08121a;width:960px;height:540px;position:relative;overflow:hidden}
    canvas{display:block}
    .hud{color:#dce7f7;padding:6px 10px;user-select:none}
    .controls{font-size:13px;color:#99b3d6}
    button{margin-left:10px}
  </style>
</head>
<body>
  <div style="width:960px;margin:8px auto;text-align:left">
    <div class="hud">Score: <span id="score">0</span> &nbsp; Lives: <span id="lives">3</span> &nbsp; Level: <span id="level">1</span>
      <button id="restart">Restart</button>
      <span class="controls">Controls: ←/→ = move, ↑ = jump, Space = shoot</span>
    </div>
    <div class="hud">
      Distance: <span id="distance">0m</span> / <span id="level-target">2000m</span>
      <div style="width:300px;height:8px;background:#2a3f54;border:1px solid #4a6b8a;display:inline-block;margin-left:10px;position:relative;">
        <div id="progress-bar" style="height:100%;background:#4a9eff;width:0%;transition:width 0.3s;"></div>
      </div>
    </div>
  </div>
  <div id="game">
    <canvas id="c" width="960" height="540"></canvas>
  </div>

<script>
/*
  Simple Contra-like single-file HTML/JS game.
  - Single player, side-scrolling stage.
  - Movement, jump, shooting. Enemies spawn and move toward player.
  - No external assets; everything drawn with canvas shapes.
*/

const canvas = document.getElementById('c');
const ctx = canvas.getContext('2d');
const W = canvas.width, H = canvas.height;

// Game state
let keys = {};
let bullets = [];
let enemies = [];
let platforms = [];
let score = 0; let lives = 3; let level = 1;
let cameraX = 0; // horizontal scroll
let worldWidth = 4000; // how long the level is
let levelDistance = 2000; // distance needed to complete current level
let maxPlayerX = 0; // track furthest player has traveled
let lastTime = 0;
let gameOver = false;

// Player
const player = {
  x: 80, y: H-120, w: 28, h: 40,
  vx: 0, vy: 0, speed: 240, onGround: false, canShoot: true, shootCooldown: 0.2
};

// Input
window.addEventListener('keydown', e=>{ keys[e.code]=true; e.preventDefault(); });
window.addEventListener('keyup', e=>{ keys[e.code]=false; });

document.getElementById('restart').addEventListener('click', init);

function init(){
  bullets=[]; enemies=[]; platforms=[]; score=0; lives=3; level=1; cameraX=0; gameOver=false;
  levelDistance = 2000; maxPlayerX = 0;
  player.x = 80; player.y = H-120; player.vx=0; player.vy=0; player.onGround=false; player.shootTimer=0;
  buildLevel();
}

function buildLevel(){
  // Base ground - we'll split this into segments to allow valleys
  let groundSegments = [{x:0, y:H-60, w:worldWidth, h:60}];

  // Generate varied terrain based on level
  const terrainSeed = level * 123; // Simple seed for consistent terrain per level

  for(let x = 0; x < worldWidth; x += 200) {
    // Ensure clear passage near level end (last 300px)
    const distanceFromEnd = worldWidth - x;
    const nearLevelEnd = distanceFromEnd < 300;

    const terrainType = nearLevelEnd ? 5 : Math.floor((Math.sin(x * 0.001 + terrainSeed) + 1) * 3); // 0-5 terrain types

    switch(terrainType) {
      case 0: // Floating platforms
        if(x > 300) {
          platforms.push({x: x, y: H-160, w: 120, h: 16});
          platforms.push({x: x + 150, y: H-220, w: 100, h: 16});
        }
        break;

      case 1: // Stepped platforms (stairs)
        if(x > 300) {
          for(let i = 0; i < 4; i++) {
            platforms.push({x: x + i*40, y: H-100 - i*30, w: 50, h: 16});
          }
        }
        break;

      case 2: // Valley with bridge
        if(x > 300 && x < worldWidth - 300) {
          // Create valley by splitting ground segments
          const valleyStart = x;
          const valleyEnd = x + 200;
          const newSegments = [];

          for(let segment of groundSegments) {
            if(segment.x + segment.w <= valleyStart || segment.x >= valleyEnd) {
              // Segment doesn't overlap with valley
              newSegments.push(segment);
            } else {
              // Segment overlaps with valley - split it
              if(segment.x < valleyStart) {
                // Add part before valley
                newSegments.push({x: segment.x, y: segment.y, w: valleyStart - segment.x, h: segment.h});
              }
              if(segment.x + segment.w > valleyEnd) {
                // Add part after valley
                newSegments.push({x: valleyEnd, y: segment.y, w: (segment.x + segment.w) - valleyEnd, h: segment.h});
              }
            }
          }
          groundSegments = newSegments;

          // Add bridge across valley
          platforms.push({x: x + 50, y: H-140, w: 100, h: 12});
        }
        break;

      case 3: // Hill with platforms
        if(x > 300) {
          // Create hill effect with elevated ground
          platforms.push({x: x, y: H-120, w: 180, h: 80});
          platforms.push({x: x + 40, y: H-180, w: 100, h: 20});
        }
        break;

      case 4: // Obstacle course
        if(x > 300 && !nearLevelEnd) {
          platforms.push({x: x + 20, y: H-100, w: 20, h: 40}); // Wall
          platforms.push({x: x + 80, y: H-140, w: 60, h: 16}); // Jump platform
          platforms.push({x: x + 160, y: H-100, w: 20, h: 40}); // Another wall
        }
        break;

      default: // Sparse floating platforms
        if(x > 300 && Math.random() > 0.6) {
          platforms.push({x: x + Math.random()*100, y: H-120 - Math.random()*80, w: 80 + Math.random()*40, h: 16});
        }
        break;
    }
  }

  // Add ground segments to platforms
  platforms.push(...groundSegments);

  // initial enemies
  spawnWave(6);
}

function spawnWave(n){
  for(let i=0;i<n;i++){
    // Spawn enemies ahead of player, not behind
    const minX = Math.max(player.x + 200, cameraX + W); // At least 200px ahead of player or off-screen right
    const maxX = Math.min(worldWidth - 100, minX + 800); // Within reasonable distance ahead
    const ex = minX + Math.random()*(maxX - minX);
    enemies.push({x:ex,y:H-100,w:28,h:36,vx:-60 - Math.random()*80, hp:1, shootTimer: Math.random()*2});
  }
}

function spawnEnemy(x,y){ enemies.push({x,y,w:28,h:36,vx:-120,hp:1,shootTimer:1}); }

// Simple rectangle collision
function rectColl(a,b){
  return a.x < b.x + b.w && a.x + a.w > b.x && a.y < b.y + b.h && a.y + a.h > b.y;
}

function update(dt){
  if(gameOver) return;

  // player movement
  player.vx = 0;
  if(keys['ArrowLeft']||keys['KeyA']) player.vx = -player.speed;
  if(keys['ArrowRight']||keys['KeyD']) player.vx = player.speed;
  if((keys['ArrowUp']||keys['KeyW']) && player.onGround){ player.vy = -420; player.onGround=false; }

  // shooting
  player.shootTimer = Math.max(0,(player.shootTimer||0)-dt);
  if((keys['Space'] || keys['KeyK']) && player.shootTimer <= 0){
    bullets.push({x:player.x + player.w, y:player.y + 10, vx:500, w:8,h:4,from:'player'});
    player.shootTimer = player.shootCooldown;
  }

  // physics
  player.vy += 1200 * dt; // gravity
  player.x += player.vx * dt;
  player.y += player.vy * dt;

  // platform collision
  player.onGround = false;
  for(let p of platforms){
    if(rectColl(player,p)){
      // simple resolution: place player on top if falling
      if(player.vy > 0 && (player.y + player.h) - player.vy*dt <= p.y + 1){
        player.y = p.y - player.h; player.vy = 0; player.onGround = true;
      } else if(player.vy < 0 && player.y >= p.y + p.h - 1){
        player.y = p.y + p.h; player.vy = 0;
      } else {
        // horizontal pushback
        if(player.vx > 0) player.x = p.x - player.w;
        if(player.vx < 0) player.x = p.x + p.w;
      }
    }
  }

  // clamp player
  player.x = Math.max(0, Math.min(worldWidth - player.w, player.x));
  if(player.y > H + 200){ // fell off
    loseLife();
  }

  // bullets
  for(let i=bullets.length-1;i>=0;i--){
    const b = bullets[i];
    b.x += b.vx*dt;
    // world bounds
    if(b.x < cameraX - 200 || b.x > cameraX + W + 200) bullets.splice(i,1);
    else{
      if(b.from==='player'){
        // hit enemies
        for(let j=enemies.length-1;j>=0;j--){
          if(rectColl(b,enemies[j])){ enemies[j].hp -= 1; bullets.splice(i,1); if(enemies[j].hp<=0){ score+=100; enemies.splice(j,1); } break; }
        }
      } else {
        // enemy bullet hits player
        if(rectColl(b,player)){ bullets.splice(i,1); loseLife(); }
      }
    }
  }

  // enemies AI
  for(let e of enemies){
    e.x += e.vx * dt;
    e.shootTimer -= dt;
    // enemy fires occasionally
    if(e.shootTimer <= 0){
      e.shootTimer = 1 + Math.random()*2;
      const dir = (player.x + player.w/2) - (e.x + e.w/2);
      const speed = 220;
      const vx = (dir>0?1:-1)*speed;
      bullets.push({x:e.x, y:e.y + 10, vx:vx, w:8,h:4,from:'enemy'});
    }
    // remove if out of world - respawn ahead of player instead of random location
    if(e.x + e.w < 0) {
      const minX = Math.max(player.x + 300, cameraX + W + 100);
      const maxX = Math.min(worldWidth - 100, minX + 600);
      if(minX < maxX) {
        e.x = minX + Math.random()*(maxX - minX);
        e.y = H-100;
        e.vx = -60 - Math.random()*80;
      } else {
        // Remove enemy if we can't respawn ahead
        enemies.splice(enemies.indexOf(e), 1);
      }
    }
  }

  // track max distance traveled
  maxPlayerX = Math.max(maxPlayerX, player.x);

  // level progression based on distance traveled (use maxPlayerX for level completion)
  if(maxPlayerX >= levelDistance){
    level++;
    levelDistance += 1500 + (level * 300); // each level gets progressively longer
    spawnWave(3+level);
    // rebuild level with new terrain
    platforms = [];
    buildLevel();
  }

  // camera follows player (clamped)
  const camTarget = Math.max(0, Math.min(worldWidth - W, player.x - W/3));
  cameraX += (camTarget - cameraX) * Math.min(1, 6*dt);
}

function loseLife(){ lives--; document.getElementById('lives').textContent = lives; if(lives<=0) {gameOver=true;}
  else { player.x = Math.max(20, cameraX + 80); player.y = H-120; player.vx=0; player.vy=0; }
}

function render(){
  // background
  ctx.fillStyle = '#0b1220'; ctx.fillRect(0,0,W,H);
  // parallax sky / mountains
  ctx.save();
  ctx.translate(-cameraX*0.2,0);
  for(let i=0;i<15;i++){
    ctx.fillStyle = '#072033';
    const height = 180 + Math.sin(i * 0.7) * 40;
    ctx.fillRect(i*300, H-320, 400, height);
  }
  ctx.restore();

  // Add distant terrain details
  ctx.save();
  ctx.translate(-cameraX*0.5,0);
  for(let i=0;i<20;i++){
    ctx.fillStyle = '#0a2540';
    const height = 60 + Math.sin(i * 1.2) * 20;
    ctx.fillRect(i*200, H-120, 250, height);
  }
  ctx.restore();

  // platforms & ground
  ctx.save(); ctx.translate(-cameraX,0);
  for(let p of platforms){
    // Different colors for different platform types
    if(p.h >= 60) {
      ctx.fillStyle = '#274156'; // Ground color
    } else if(p.h >= 40) {
      ctx.fillStyle = '#3a5f4a'; // Hill/elevated ground
    } else if(p.h >= 20) {
      ctx.fillStyle = '#4a3a56'; // Medium platforms
    } else {
      ctx.fillStyle = '#213a4a'; // Small platforms
    }
    ctx.fillRect(p.x, p.y, p.w, p.h);

    // Add some texture/detail to larger platforms
    if(p.w > 100 && p.h > 20) {
      ctx.fillStyle = 'rgba(255,255,255,0.1)';
      ctx.fillRect(p.x, p.y, p.w, 2); // Top highlight
    }
  }

  // draw enemies
  for(let e of enemies){
    ctx.fillStyle = 'var(--enemy)'; ctx.fillRect(e.x, e.y, e.w, e.h);
    // simple eye
    ctx.fillStyle = '#222'; ctx.fillRect(e.x+6, e.y+8, 6,6);
  }

  // draw bullets (world coords)
  for(let b of bullets){ if(b.from==='player') ctx.fillStyle='var(--bullet)'; else ctx.fillStyle='#ffcccb'; ctx.fillRect(b.x,b.y,b.w,b.h); }

  // draw player
  ctx.fillStyle = 'var(--player)'; ctx.fillRect(player.x, player.y, player.w, player.h);
  // gun
  ctx.fillStyle = '#b97c3c'; ctx.fillRect(player.x+player.w, player.y+10, 12,6);

  ctx.restore();

  // HUD
  ctx.fillStyle = '#bcd9ff'; ctx.font='18px monospace'; ctx.fillText('SCORE: ' + score, 12, 26);
  ctx.fillText('LIVES: ' + lives, 12, 48);
  ctx.fillText('LEVEL: ' + level, 12, 70);

  if(gameOver){ ctx.fillStyle='rgba(4,8,12,0.75)'; ctx.fillRect(0,0,W,H);
    ctx.fillStyle='#fff'; ctx.font='36px monospace'; ctx.fillText('GAME OVER', W/2-120, H/2-10);
    ctx.font='18px monospace'; ctx.fillText('Press Restart to play again', W/2-120, H/2+24);
  }
}

function loop(ts){
  if(!lastTime) lastTime = ts;
  const dt = Math.min(1/30, (ts - lastTime)/1000);
  lastTime = ts;
  update(dt);
  render();
  requestAnimationFrame(loop);
}

// small spawn growth over time
setInterval(()=>{ if(!gameOver) spawnWave(1); }, 5000);

// start
init();
requestAnimationFrame(loop);

// update HUD
setInterval(()=>{
  document.getElementById('score').textContent = score;
  document.getElementById('level').textContent = level;

  // Calculate and update progress using current player position (not max)
  // Calculate level boundaries correctly
  let currentLevelStart = 0;
  for(let i = 1; i < level; i++) {
    if(i === 1) {
      currentLevelStart += 2000;
    } else {
      currentLevelStart += 1500 + (i * 300);
    }
  }
  const currentLevelEnd = levelDistance;
  const progress = Math.min(100, Math.max(0, ((player.x - currentLevelStart) / (currentLevelEnd - currentLevelStart)) * 100));

  // Display distance in meters/km
  const currentDistance = Math.max(0, player.x - currentLevelStart);
  const totalLevelDistance = currentLevelEnd - currentLevelStart;

  const formatDistance = (dist) => {
    if (dist >= 1000) {
      return (dist / 1000).toFixed(1) + 'km';
    } else {
      return Math.floor(dist) + 'm';
    }
  };

  document.getElementById('distance').textContent = formatDistance(currentDistance);
  document.getElementById('level-target').textContent = formatDistance(totalLevelDistance);
  document.getElementById('progress-bar').style.width = progress + '%';
}, 200);

</script>
</body>
</html>
